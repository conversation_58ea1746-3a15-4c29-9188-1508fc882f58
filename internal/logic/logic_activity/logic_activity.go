package logic_activity

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"activitysrv/internal/model"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"time"
)

// StageConfig 阶段配置
type StageConfig struct {
	StageId    int32           `json:"stage_id"`   // 阶段ID
	Conditions map[int32]int64 `json:"conditions"` // 完成条件，key为指标类型，value为需要达到的值
	Rewards    []RewardItem    `json:"rewards"`    // 奖励列表
}

// RewardItem 奖励物品
type RewardItem struct {
	ItemId int32 `json:"item_id"` // 物品ID
	Count  int32 `json:"count"`   // 数量
}

// ActivityLogic 爆护之路业务逻辑
type ActivityLogic struct {
}

// NewActivityLogic 创建爆护之路业务逻辑实例
func NewActivityLogic() *ActivityLogic {
	return &ActivityLogic{}
}

// HandleEvent 处理事件 - 实现ActivityHandler接口
func (logic *ActivityLogic) HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error {
	entry := logx.NewLogEntry(ctx)

	// 1. 获取所有活动配置
	allActivities := cmodel.GetAllActivity(consul_config.WithGrpcCtx(ctx))
	if allActivities == nil {
		entry.Debugf("活动配置不存在")
		return nil
	}

	// 2. 筛选当前开启且支持该事件类型的活动
	activeActivities := filterActiveActivitiesForEvent(allActivities, event)
	if len(activeActivities) == 0 {
		entry.Debugf("没有活跃的活动需要处理此事件: eventType=%v", event.EventType)
		return nil
	}

	for _, activityCfg := range activeActivities {
		err := processActivityEvent(ctx, playerId, event, activityCfg)
		if err != nil {
			entry.Errorf("处理活动事件失败: activityId=%d, err=%v", activityCfg.Id, err)
		}
	}

	return nil
}

// GetProgress 获取活动进度 - 实现ActivityHandler接口
func (logic *ActivityLogic) GetProgress(ctx context.Context, playerId uint64, req *activityPB.GetActivityProgressReq) ([]*activityPB.ActivityProgress, error) {
	entry := logx.NewLogEntry(ctx)

	activityId := int64(req.GetActivityId())
	// 获取活动配置
	activityCfg := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, fmt.Errorf("活动配置不存在")
	}

	// 获取当前周期
	currentCycle, err := dao_activity.CheckAndCreateCycleIfNeeded(ctx, activityCfg)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}

	// 获取当前周期的用户数据
	currentUserData, err := dao_activity.GetUserData(ctx, activityId, playerId, currentCycle.CycleId)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期用户数据失败: %w", err)
	}

	var res []*activityPB.ActivityProgress
	// 构建进度数据
	progress := &activityPB.ActivityProgress{
		ActivityId:     req.GetActivityId(),
		CurrentCycleId: currentCycle.CycleId,
		CycleEndTime:   currentCycle.EndTime,
		Metrics:        currentUserData.Metrics,
		ClaimedRecords: currentUserData.GetClaimedStagesList(),
	}

	res = append(res, progress)

	// 获取上个周期数据（如果存在）
	if currentCycle.CycleId > 1 {
		previousCycleId := currentCycle.CycleId - 1
		previousUserData, err := dao_activity.GetUserData(ctx, activityId, playerId, previousCycleId)
		if err != nil {
			entry.Warnf("获取上个周期用户数据失败: %v", err)
		} else {
			previousCycle := &activityPB.ActivityProgress{
				ActivityId:     req.GetActivityId(),
				CurrentCycleId: previousCycleId,
				Metrics:        previousUserData.Metrics,
				ClaimedRecords: previousUserData.GetClaimedStagesList(),
			}
			res = append(res, previousCycle)
		}
	}

	entry.Debugf("成功获取活动进度: activityId=%d, playerId=%d, currentCycleId=%d",
		req.GetActivityId(), playerId, currentCycle.CycleId)

	return res, nil
}

// ClaimReward 领取奖励
func (logic *ActivityLogic) ClaimReward(ctx context.Context, playerId uint64, req *activityPB.ClaimActivityRewardReq) (*activityPB.ClaimActivityRewardRsp, error) {
	entry := logx.NewLogEntry(ctx)

	activityId := int64(req.GetActivityId())

	// 获取活动配置
	activityCfg := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, fmt.Errorf("爆护之路活动配置不存在")
	}

	// 校验活动时间
	currentCycle, err := dao_activity.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}
	if currentCycle == nil {
		return nil, fmt.Errorf("当前没有活跃的活动周期")
	}

	// 检查周期是否有效
	if req.GetCycleId() != currentCycle.CycleId && req.GetCycleId() != currentCycle.CycleId-1 {
		return nil, fmt.Errorf("周期 %d 的奖励已过期，仅支持当前周期和上一周期的奖励领取", req.GetCycleId())
	}

	// 加锁
	unlock := dlm.LockKey(config.UserActivityLockKey(activityId, playerId))
	defer unlock()

	// 获取用户数据
	userData, err := dao_activity.GetUserData(ctx, activityId, playerId, req.GetCycleId())
	if err != nil {
		return nil, fmt.Errorf("获取用户数据失败: %w", err)
	}

	// 筛选玩家当前可以领取的阶段
	availableStages, err := getAvailableStagesForClaim(ctx, activityCfg, userData)
	if err != nil {
		return nil, fmt.Errorf("获取可领取阶段失败: %w", err)
	}

	if len(availableStages) == 0 {
		return nil, fmt.Errorf("当前没有可领取的奖励阶段")
	}

	// 序列化奖励和阶段
	now := timex.Now().Unix()
	var rewardList []*commonPB.ItemBase
	stages := make([]int32, 0, len(availableStages))
	for stageId, rewards := range availableStages {
		stages = append(stages, int32(stageId))
		userData.ClaimedStages[stageId] = now
		for _, reward := range rewards {
			rewardList = append(rewardList, &commonPB.ItemBase{
				ItemId:    reward.ItemId,
				ItemCount: reward.Count,
			})
		}
	}

	// 先更新玩家领取阶段，再实际领取
	err = dao_activity.SaveUserData(ctx, playerId, activityCfg, currentCycle, userData)
	if err != nil {
		return nil, fmt.Errorf("更新用户数据失败: %w", err)
	}

	rewardInfo, err := item_kit.SendReward(ctx, playerId, rewardList, commonPB.ITEM_SOURCE_TYPE_IST_ACTIVITY_REWARD, false)
	if err != nil {
		entry.Errorf("发放奖励失败: %v rewardInfo=%v", err, rewardInfo)
		return nil, fmt.Errorf("发放奖励失败: %w", err)
	}

	res := &activityPB.ClaimActivityRewardRsp{
		ActivityId: req.GetActivityId(),
		StageId:    stages,
	}

	return res, nil
}

// getAvailableStagesForClaim 获取玩家当前可以领取的所有阶段
func getAvailableStagesForClaim(ctx context.Context, activityCfg *cmodel.Activity, userData *model.UserActivityData) (map[int32][]cmodel.StageRewardsRewards, error) {

	allStageConfigs := cmodel.GetAllStages(consul_config.WithGrpcCtx(ctx))
	if allStageConfigs == nil {
		return nil, fmt.Errorf("获取阶段配置失败")
	}

	res := make(map[int32][]cmodel.StageRewardsRewards)
	curProgress := userData.Metrics[activityCfg.Target]
	for _, stageCfg := range allStageConfigs {
		// 检查是否是当前活动的阶段
		if stageCfg.ActivityId != activityCfg.Id {
			continue
		}

		// 检查是否已经领取过
		if userData.IsStageClaimedStage(int32(stageCfg.StageId)) {
			continue
		}

		// 检查是否满足领取条件
		if curProgress >= stageCfg.Value {
			stageRewards := cmodel.GetStageRewards(stageCfg.StageId, consul_config.WithGrpcCtx(ctx))
			if stageRewards == nil {
				return nil, fmt.Errorf("获取阶段奖励配置失败: %d", stageCfg.StageId)
			}
			res[int32(stageCfg.StageId)] = stageRewards.Rewards

		}
	}

	return res, nil
}

// isActivityActive 检查活动是否活跃
func isActivityActive(cfg *cmodel.Activity) bool {
	// 检查活动时间
	now := time.Now().Unix()
	if now < cfg.OpenAt || (cfg.CloseAt > 0 && now > cfg.CloseAt) {
		return false
	}

	return true
}

// hasActivityMetrics 检查事件是否包含活动相关指标
func hasActivityMetrics(event *commonPB.EventCommon, cfg *cmodel.Activity) bool {
	if _, exists := event.IntData[cfg.Target]; exists {
		return true
	}
	return false
}

// filterActiveActivitiesForEvent 筛选当前开启且支持该事件类型的活动
func filterActiveActivitiesForEvent(allActivities map[int64]*cmodel.Activity, event *commonPB.EventCommon) []*cmodel.Activity {
	var activeActivities []*cmodel.Activity

	for _, activityCfg := range allActivities {
		// 检查活动是否活跃
		if !isActivityActive(activityCfg) {
			continue
		}

		// 检查活动指标是否包含在事件中
		if !hasActivityMetrics(event, activityCfg) {
			continue
		}

		activeActivities = append(activeActivities, activityCfg)
	}

	return activeActivities
}

// processActivityEvent 处理单个活动的事件
func processActivityEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error {
	// 1. 周期管理：检查当前周期是否已结束，如果结束则自动创建新周期
	currentCycle, err := dao_activity.CheckAndCreateCycleIfNeeded(ctx, activityCfg)
	if err != nil {
		return fmt.Errorf("检查或创建活动周期失败: activityId=%d, %w", activityCfg.Id, err)
	}

	// 2. 加锁
	unlock := dlm.LockKey(config.UserActivityLockKey(activityCfg.Id, playerId))
	defer unlock()

	// 3. 获取用户数据
	currentUserData, err := dao_activity.GetUserData(ctx, activityCfg.Id, playerId, currentCycle.CycleId)
	if err != nil {
		return fmt.Errorf("获取用户数据失败: %w", err)
	}

	// 4. 更新用户指标
	curNum := currentUserData.Metrics[activityCfg.Target]
	oprNum := event.GetIntData()[activityCfg.Target]
	newTarget := operate.SumAddVal(commonPB.SUM_ADD(activityCfg.Update), curNum, oprNum)

	// 5. 保存更新后的用户数据
	currentUserData.Metrics[activityCfg.Target] = newTarget
	err = dao_activity.SaveUserData(ctx, playerId, activityCfg, currentCycle, currentUserData)

	return err
}
