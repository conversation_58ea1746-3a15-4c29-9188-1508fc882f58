package dao_activity

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"github.com/go-redis/redis/v8"
	"github.com/goccy/go-json"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// GetUserData 获取用户活动数据
func GetUserData(ctx context.Context, activityId int64, userId uint64, cycleId int32) (*model.UserActivityData, error) {
	key := config.PlayerActivityDataKey(activityId, userId, cycleId)
	redisCli := redisx.GetActivityCli()

	// 使用Get
	data, err := redisCli.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return model.NewUserActivityData(), nil
		}
		return nil, fmt.Errorf("获取用户活动数据失败: %w", err)
	}

	userData := &model.UserActivityData{}
	err = json.Unmarshal([]byte(data), userData)
	if err != nil {
		return nil, fmt.Errorf("解析用户活动数据失败: %w", err)
	}

	return userData, err
}

// SaveUserData 保存用户活动数据
func SaveUserData(ctx context.Context, playerId uint64, activityCfg *cmodel.Activity, cycle *model.ActivityCycle, userData *model.UserActivityData) error {
	key := config.PlayerActivityDataKey(activityCfg.Id, playerId, cycle.CycleId)

	// 更新时间戳
	userData.UpdatedAt = time.Now().Unix()

	data, err := json.Marshal(userData)
	if err != nil {
		return err
	}

	ttl := config.CalculateActivityTTL(cycle)
	err = redisx.GetActivityCli().SetNX(ctx, key, data, ttl).Err()

	return err
}
